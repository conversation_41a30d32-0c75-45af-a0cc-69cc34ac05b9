FROM eclipse-temurin:23-jre-alpine

# Install Python and pip
RUN apk add --no-cache python3 py3-pip

## Create a group and user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Copy requirements.txt and install Python dependencies as root
COPY requirements.txt /tmp/requirements.txt
RUN pip3 install --no-cache-dir -r /tmp/requirements.txt && rm /tmp/requirements.txt

#
## Tell docker that all future commands should run as the appuser user
USER appuser

WORKDIR /app
COPY --chown=appuser:appgroup ./target/*.jar app.jar
COPY --chown=appuser:appgroup ./docling_extractor.py docling_extractor.py

EXPOSE 8080

ENTRYPOINT ["java", "-XX:+UseContainerSupport", "-XX:MaxRAMPercentage=75.0", "-jar", "app.jar"]
