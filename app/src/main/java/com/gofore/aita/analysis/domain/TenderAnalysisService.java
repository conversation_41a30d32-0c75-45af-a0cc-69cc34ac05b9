package com.gofore.aita.analysis.domain;

import com.gofore.aita.analysis.api.IAnalysisService;
import com.gofore.aita.core.domain.SystemConfigurationService;
import com.gofore.aita.core.domain.models.SystemConfiguration;
import com.gofore.aita.extraction.domain.FileExtractionService;
import com.gofore.aita.extraction.domain.TextProcessingService;
import com.gofore.aita.extraction.domain.TextProcessingService.TextWithTokenCount;
import com.gofore.aita.extraction.models.ExtractedDocument;
import com.gofore.aita.tender.data.TenderRepository;
import com.gofore.aita.tender.domain.exceptions.ResourceNotFoundException;
import com.gofore.aita.tender.domain.mapper.AnalysisResultMapper;
import com.gofore.aita.tender.domain.models.AnalysisResult;
import com.gofore.aita.tender.domain.models.Tender;
import com.gofore.aita.tender.domain.models.TenderStatus;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class TenderAnalysisService {

  private final TenderRepository tenderRepository;
  private final FileExtractionService fileExtraction;
  private final TextProcessingService textProcessing;
  private final SystemConfigurationService configService;
  private final IAnalysisService analysisService;
  private final AnalysisResultMapper resultMapper;

  /**
   * Analyzes a tender, processing the text, and persisting the result.
   *
   * @param tenderId The ID of the tender to analyze
   * @return The analysis result
   * @throws ResourceNotFoundException if the tender is not found
   */
  @Transactional
  public AnalysisResult analyzeTender(String tenderId) {
    log.info("Starting analysis for tender {}", tenderId);

    Tender tender =
        tenderRepository
            .findById(tenderId)
            .orElseThrow(() -> new ResourceNotFoundException("Tender not found: " + tenderId));

    tender.setStatus(TenderStatus.ANALYZING);
    tender = tenderRepository.save(tender);

    // extract or use extracted document text
    List<ExtractedDocument> allDocs = fileExtraction.processFilesForExtraction(tender);

    // filter out any “forms”
    List<ExtractedDocument> docs =
        allDocs.stream()
            .filter(doc -> doc.getIsForm() == null || !doc.getIsForm())
            .collect(Collectors.toList());

    // build the full prompt text and token count
    String description = tender.getDescription();
    TextWithTokenCount textWithTokens = textProcessing.combineTexts(description, docs);
    int tokenCount = textWithTokens.getTokenCount();
    log.debug("Total tokens: {}", tokenCount);
    SystemConfiguration cfg = configService.get();
    com.gofore.aita.analysis.models.AnalysisResult analysisResult;
    int stepPromptTokens = 0;
    int stepCompletionTokens = 0;

    if (tokenCount < 100000) {
      log.info(
          "Token count ({}) does not exceeds 100000, using full text for analysis.", tokenCount);
      analysisResult =
          analysisService.analyzeTender(
              textWithTokens.getText(), cfg.getAiAnalysisAnalysisPrompt());
    } else {
      log.info(
          "Token count ({}) exceeds 100000, using stepwise analysis for {} documents.",
          tokenCount,
          docs.size());
      List<com.gofore.aita.analysis.models.AnalysisResult> analysisResults = new ArrayList<>();

      for (ExtractedDocument doc : docs) {
        textWithTokens = textProcessing.combineTexts(description, List.of(doc));
        tokenCount = textWithTokens.getTokenCount();
        log.debug("Total tokens: {} for description and file {}", tokenCount, doc.getFileName());

        com.gofore.aita.analysis.models.AnalysisResult stepAnalysisResult =
            analysisService.analyzeTender(
                textWithTokens.getText(), cfg.getAiAnalysisAnalysisPrompt());
        analysisResults.add(stepAnalysisResult);
        stepPromptTokens += stepAnalysisResult.getPromptTokens();
        stepCompletionTokens += stepAnalysisResult.getCompletionTokens();
      }

      // combine the results
      analysisResult = analysisService.combineAnalysisResults(analysisResults);
      analysisResult.setPromptTokens(analysisResult.getPromptTokens() + stepPromptTokens);
      analysisResult.setCompletionTokens(
          analysisResult.getCompletionTokens() + stepCompletionTokens);
    }

    // map, persist, and return
    AnalysisResult out = resultMapper.map(analysisResult);
    out.setLastUpdatedTime(OffsetDateTime.now().truncatedTo(ChronoUnit.SECONDS).toString());

    // check if analysis result is valid markdown
    sanitizeAnalysis(out);

    tender.setAnalysisResult(out);
    tender.setStatus(TenderStatus.ANALYZED);
    tenderRepository.save(tender);

    log.info("Completed analysis for tender {}", tenderId);
    return out;
  }

  private void sanitizeAnalysis(AnalysisResult out) {
    if (out == null || out.getAnalysisResult().isEmpty()) {
      return;
    }

    // search "# 1. Allgemeine Rahmenbedingungen" and delete everything before
    int index = out.getAnalysisResult().indexOf("# 1. Allgemeine Rahmenbedingungen");
    if (index > 0) {
      out.setAnalysisResult(out.getAnalysisResult().substring(index));
    }

    if (out.getAnalysisResult().endsWith("```")) {
      out.setAnalysisResult(
          out.getAnalysisResult().substring(0, out.getAnalysisResult().length() - 3));
    }
  }
}
