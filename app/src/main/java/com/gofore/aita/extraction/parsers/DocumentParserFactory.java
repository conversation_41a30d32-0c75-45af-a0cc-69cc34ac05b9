package com.gofore.aita.extraction.parsers;

import com.gofore.aita.extraction.api.IDocumentParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;

@Slf4j
public final class DocumentParserFactory {

  private DocumentParserFactory() {
    /* no instances */
  }

  public static IDocumentParser getParser(String extension) {
    String ext = extension == null ? "" : extension.trim().toLowerCase();

    switch (ext) {
      case "pdf":
        return new PdfParser();
      case "doc":
      case "docx":
      case "ppt":
      case "pptx":
        return new OfficeParser();
      case "xls":
      case "xlsx":
        return new DoclingParser();
      case "txt":
      case "csv":
        return new PlainTextParser();
      default:
        log.warn("No parser available for extension: " + ext);
        return new AutoDetectDefaultParser();
    }
  }

  public static String extensionFrom(String fileName) {
    return FilenameUtils.getExtension(fileName);
  }
}
