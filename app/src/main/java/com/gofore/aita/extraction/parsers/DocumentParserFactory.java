package com.gofore.aita.extraction.parsers;

import com.gofore.aita.extraction.api.IDocumentParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;

@Slf4j
public final class DocumentParserFactory {

  private DocumentParserFactory() {
    /* no instances */
  }

  public static IDocumentParser getParser(String extension) {
    String ext = extension == null ? "" : extension.trim().toLowerCase();

    // Use Docling as primary parser for all supported formats with fallback
    switch (ext) {
      case "pdf":
      case "doc":
      case "docx":
      case "ppt":
      case "pptx":
      case "xls":
      case "xlsx":
      case "txt":
      case "csv":
        log.debug("Creating DoclingParser with fallback for extension: {}", ext);
        return DoclingParserFactory.createDoclingParser(ext);
      default:
        log.warn("No specific parser available for extension: {}. Using DoclingParser with AutoDetect fallback.", ext);
        return DoclingParserFactory.createDoclingParser(ext);
    }
  }

  public static String extensionFrom(String fileName) {
    return FilenameUtils.getExtension(fileName);
  }
}
