package com.gofore.aita.extraction.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for executing external processes, particularly Python scripts. Handles process
 * execution, output capture, and error handling.
 */
@Slf4j
public class ProcessExecutor {

  private static final int DEFAULT_TIMEOUT_SECONDS = 300; // 5 minutes
  private static final String PYTHON_COMMAND = "python3";

  /** Result of a process execution. */
  public static class ProcessResult {
    private final int exitCode;
    private final String stdout;
    private final String stderr;
    private final boolean timedOut;

    public ProcessResult(int exitCode, String stdout, String stderr, boolean timedOut) {
      this.exitCode = exitCode;
      this.stdout = stdout;
      this.stderr = stderr;
      this.timedOut = timedOut;
    }

    public int getExitCode() {
      return exitCode;
    }

    public String getStdout() {
      return stdout;
    }

    public String getStderr() {
      return stderr;
    }

    public boolean isTimedOut() {
      return timedOut;
    }

    public boolean isSuccess() {
      return exitCode == 0 && !timedOut;
    }

    @Override
    public String toString() {
      return String.format(
          "ProcessResult{exitCode=%d, timedOut=%s, stdout='%s', stderr='%s'}",
          exitCode, timedOut, stdout.substring(0, Math.min(100, stdout.length())), stderr);
    }
  }

  /**
   * Execute a Python script with the given arguments.
   *
   * @param scriptPath Path to the Python script
   * @param args Arguments to pass to the script
   * @return ProcessResult containing the execution results
   */
  public static ProcessResult executePythonScript(String scriptPath, String... args) {
    return executePythonScript(scriptPath, DEFAULT_TIMEOUT_SECONDS, args);
  }

  /**
   * Execute a Python script with the given arguments and timeout.
   *
   * @param scriptPath Path to the Python script
   * @param timeoutSeconds Timeout in seconds
   * @param args Arguments to pass to the script
   * @return ProcessResult containing the execution results
   */
  public static ProcessResult executePythonScript(
      String scriptPath, int timeoutSeconds, String... args) {
    List<String> command = new ArrayList<>();
    command.add(PYTHON_COMMAND);
    command.add(scriptPath);

    if (args != null) {
      for (String arg : args) {
        command.add(arg);
      }
    }

    return executeCommand(command, timeoutSeconds);
  }

  /**
   * Execute a command with the given timeout.
   *
   * @param command Command and arguments as a list
   * @param timeoutSeconds Timeout in seconds
   * @return ProcessResult containing the execution results
   */
  public static ProcessResult executeCommand(List<String> command, int timeoutSeconds) {
    log.debug("Executing command: {}", String.join(" ", command));

    ProcessBuilder processBuilder = new ProcessBuilder(command);
    processBuilder.redirectErrorStream(false);

    // Set working directory to the application root
    File workingDir = new File(System.getProperty("user.dir"));
    processBuilder.directory(workingDir);

    StringBuilder stdout = new StringBuilder();
    StringBuilder stderr = new StringBuilder();
    boolean timedOut = false;
    int exitCode = -1;

    try {
      Process process = processBuilder.start();

      // Read stdout in a separate thread
      Thread stdoutReader =
          new Thread(
              () -> {
                try (BufferedReader reader =
                    new BufferedReader(
                        new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                  String line;
                  while ((line = reader.readLine()) != null) {
                    stdout.append(line).append("\n");
                  }
                } catch (IOException e) {
                  log.warn("Error reading stdout: {}", e.getMessage());
                }
              });

      // Read stderr in a separate thread
      Thread stderrReader =
          new Thread(
              () -> {
                try (BufferedReader reader =
                    new BufferedReader(
                        new InputStreamReader(process.getErrorStream(), StandardCharsets.UTF_8))) {
                  String line;
                  while ((line = reader.readLine()) != null) {
                    stderr.append(line).append("\n");
                  }
                } catch (IOException e) {
                  log.warn("Error reading stderr: {}", e.getMessage());
                }
              });

      stdoutReader.start();
      stderrReader.start();

      // Wait for process to complete with timeout
      boolean finished = process.waitFor(timeoutSeconds, TimeUnit.SECONDS);

      if (!finished) {
        log.warn("Process timed out after {} seconds, destroying it", timeoutSeconds);
        process.destroyForcibly();
        timedOut = true;
        exitCode = -1;
      } else {
        exitCode = process.exitValue();
      }

      // Wait for reader threads to complete
      stdoutReader.join(5000); // Wait up to 5 seconds for readers to finish
      stderrReader.join(5000);

    } catch (IOException e) {
      log.error("Error starting process: {}", e.getMessage(), e);
      stderr.append("Process execution error: ").append(e.getMessage());
      exitCode = -1;
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      log.error("Process execution interrupted: {}", e.getMessage(), e);
      stderr.append("Process execution interrupted: ").append(e.getMessage());
      exitCode = -1;
    }

    ProcessResult result =
        new ProcessResult(exitCode, stdout.toString(), stderr.toString(), timedOut);

    log.debug("Process execution completed: {}", result);

    return result;
  }

  /**
   * Check if Python is available on the system.
   *
   * @return true if Python is available, false otherwise
   */
  public static boolean isPythonAvailable() {
    try {
      ProcessResult result = executeCommand(List.of(PYTHON_COMMAND, "--version"), 10);
      return result.isSuccess();
    } catch (Exception e) {
      log.debug("Python availability check failed: {}", e.getMessage());
      return false;
    }
  }

  /**
   * Check if a Python package is installed.
   *
   * @param packageName Name of the package to check
   * @return true if the package is installed, false otherwise
   */
  public static boolean isPythonPackageInstalled(String packageName) {
    try {
      ProcessResult result =
          executeCommand(List.of(PYTHON_COMMAND, "-c", "import " + packageName), 10);
      return result.isSuccess();
    } catch (Exception e) {
      log.debug("Python package check failed for {}: {}", packageName, e.getMessage());
      return false;
    }
  }
}
