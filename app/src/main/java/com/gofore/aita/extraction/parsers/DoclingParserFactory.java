package com.gofore.aita.extraction.parsers;

import com.gofore.aita.extraction.api.IDocumentParser;
import lombok.extern.slf4j.Slf4j;

/**
 * Factory for creating DoclingParser instances with appropriate fallback parsers.
 * This factory creates DoclingParser instances that use traditional parsers as fallbacks
 * when <PERSON><PERSON> fails to process a document.
 */
@Slf4j
public final class DoclingParserFactory {

  private DoclingParserFactory() {
    /* no instances */
  }

  /**
   * Create a DoclingParser with appropriate fallback for the given file extension.
   *
   * @param extension File extension (without dot)
   * @return DoclingParser with appropriate fallback
   */
  public static IDocumentParser createDoclingParser(String extension) {
    String ext = extension == null ? "" : extension.trim().toLowerCase();
    
    IDocumentParser fallbackParser = createFallbackParser(ext);
    return new DoclingParser(fallbackParser, ext);
  }

  /**
   * Create the appropriate fallback parser for the given extension.
   *
   * @param extension File extension (normalized to lowercase)
   * @return Appropriate fallback parser
   */
  private static IDocumentParser createFallbackParser(String extension) {
    switch (extension) {
      case "pdf":
        log.debug("Creating PdfParser as fallback for PDF files");
        return new PdfParser();
        
      case "doc":
      case "docx":
      case "ppt":
      case "pptx":
        log.debug("Creating OfficeParser as fallback for {} files", extension.toUpperCase());
        return new OfficeParser();
        
      case "xls":
      case "xlsx":
        log.debug("Creating ExcelParser as fallback for {} files", extension.toUpperCase());
        return new ExcelParser();
        
      case "txt":
      case "csv":
        log.debug("Creating PlainTextParser as fallback for {} files", extension.toUpperCase());
        return new PlainTextParser();
        
      default:
        log.debug("Creating AutoDetectDefaultParser as fallback for unknown extension: {}", extension);
        return new AutoDetectDefaultParser();
    }
  }

  /**
   * Check if Docling should be used for the given extension.
   * Currently, Docling is used for all supported file types.
   *
   * @param extension File extension
   * @return true if Docling should be used (always true for now)
   */
  public static boolean shouldUseDocling(String extension) {
    // For now, we always try Docling first for all file types
    // This could be made configurable in the future
    return true;
  }

  /**
   * Get a list of file extensions that are optimally supported by Docling.
   * These extensions will get the best results from Docling.
   *
   * @return Array of file extensions
   */
  public static String[] getOptimalDoclingExtensions() {
    return new String[]{
        "pdf", "doc", "docx", "ppt", "pptx", 
        "xls", "xlsx", "txt", "csv", "md", 
        "html", "htm", "rtf"
    };
  }

  /**
   * Check if the given extension is optimally supported by Docling.
   *
   * @param extension File extension to check
   * @return true if extension is optimally supported
   */
  public static boolean isOptimalForDocling(String extension) {
    if (extension == null) {
      return false;
    }
    
    String ext = extension.trim().toLowerCase();
    String[] optimal = getOptimalDoclingExtensions();
    
    for (String optimalExt : optimal) {
      if (optimalExt.equals(ext)) {
        return true;
      }
    }
    
    return false;
  }
}
