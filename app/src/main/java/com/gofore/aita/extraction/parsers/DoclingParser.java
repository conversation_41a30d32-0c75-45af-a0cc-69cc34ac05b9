package com.gofore.aita.extraction.parsers;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gofore.aita.extraction.api.IDocumentParser;
import com.gofore.aita.extraction.utils.ProcessExecutor;
import java.io.ByteArrayInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.metadata.TikaCoreProperties;
import org.xml.sax.SAXException;

/**
 * Document parser that uses the docling Python library via process execution.
 * Supports all document formats (PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX, TXT, CSV)
 * with fallback to traditional parsers when Docling fails.
 */
@Slf4j
public class DoclingParser implements IDocumentParser {

  private static final String DOCLING_SCRIPT_PATH = "docling_extractor.py";
  private static final ObjectMapper objectMapper = new ObjectMapper();
  private static final int PROCESS_TIMEOUT_SECONDS = 300; // 5 minutes

  private final IDocumentParser fallbackParser;
  private final String supportedExtension;

  /**
   * Constructor with fallback parser for specific file extension.
   *
   * @param fallbackParser Parser to use when Docling fails
   * @param supportedExtension File extension this parser handles (for logging)
   */
  public DoclingParser(IDocumentParser fallbackParser, String supportedExtension) {
    this.fallbackParser = fallbackParser;
    this.supportedExtension = supportedExtension;
  }

  /**
   * Default constructor for backward compatibility (no fallback).
   */
  public DoclingParser() {
    this.fallbackParser = null;
    this.supportedExtension = "unknown";
  }

  /** Result from the docling Python script. */
  private static class DoclingResult {
    private boolean success;
    private String error;
    private String text;
    private JsonNode metadata;

    // Getters
    public boolean isSuccess() {
      return success;
    }

    public String getError() {
      return error;
    }

    public String getText() {
      return text;
    }

    public JsonNode getMetadata() {
      return metadata;
    }
  }

  @Override
  public String parseToXhtml(InputStream in, Metadata metadata)
      throws IOException, TikaException, SAXException {
    return parseDocumentWithFallback(in, metadata, "xhtml");
  }

  @Override
  public String parseToText(InputStream in, Metadata metadata)
      throws IOException, TikaException, SAXException {
    return parseDocumentWithFallback(in, metadata, "text");
  }

  /**
   * Parse document with Docling first, then fallback to traditional parser if needed.
   *
   * @param in Input stream of the document
   * @param metadata Tika metadata
   * @param outputFormat Output format ("text" or "xhtml")
   * @return Parsed content
   * @throws IOException If file operations fail
   * @throws TikaException If parsing fails
   * @throws SAXException If XML parsing fails
   */
  private String parseDocumentWithFallback(InputStream in, Metadata metadata, String outputFormat)
      throws IOException, TikaException, SAXException {

    String fileName = metadata.get(TikaCoreProperties.RESOURCE_NAME_KEY);

    // Buffer the input stream so we can use it for both Docling and fallback
    byte[] buffer;
    try {
      buffer = in.readAllBytes();
    } catch (IOException e) {
      throw new TikaException("Failed to read input stream: " + e.getMessage(), e);
    }

    try {
      // Try Docling first
      log.debug("Attempting to parse {} with Docling for format: {}", fileName, outputFormat);
      try (ByteArrayInputStream doclingStream = new ByteArrayInputStream(buffer)) {
        String result = parseDocument(doclingStream, metadata, outputFormat);
        log.info("Successfully parsed {} with Docling ({} characters)", fileName, result.length());
        return result;
      }

    } catch (Exception e) {
      log.warn("Docling parsing failed for {}: {}. Attempting fallback parser.", fileName, e.getMessage());

      if (fallbackParser != null) {
        try {
          // Use fallback parser with fresh stream
          try (ByteArrayInputStream fallbackStream = new ByteArrayInputStream(buffer)) {
            String result;
            if ("xhtml".equals(outputFormat)) {
              result = fallbackParser.parseToXhtml(fallbackStream, metadata);
            } else {
              result = fallbackParser.parseToText(fallbackStream, metadata);
            }

            log.info("Successfully parsed {} with fallback parser ({} characters)", fileName, result.length());
            return result;
          }

        } catch (Exception fallbackException) {
          log.error("Fallback parser also failed for {}: {}", fileName, fallbackException.getMessage());
          throw new TikaException("Both Docling and fallback parser failed: " + e.getMessage() +
                                 "; Fallback error: " + fallbackException.getMessage(), e);
        }
      } else {
        log.error("No fallback parser available for {}", fileName);
        throw new TikaException("Docling parsing failed and no fallback parser available: " + e.getMessage(), e);
      }
    }
  }

  /**
   * Parse document using docling Python script.
   *
   * @param in Input stream of the document
   * @param metadata Tika metadata
   * @param outputFormat Output format ("text" or "xhtml")
   * @return Parsed content
   * @throws IOException If file operations fail
   * @throws TikaException If parsing fails
   */
  private String parseDocument(InputStream in, Metadata metadata, String outputFormat)
      throws IOException, TikaException {

    // Check if Python and docling are available
    if (!ProcessExecutor.isPythonAvailable()) {
      throw new TikaException("Python is not available on the system");
    }

    if (!ProcessExecutor.isPythonPackageInstalled("docling")) {
      log.warn("Docling package not installed, falling back to error message");
      throw new TikaException(
          "Docling Python package is not installed. Please install it with: pip install docling");
    }

    // Create temporary file for the input document
    Path tempFile = null;
    try {
      // Create temp file with appropriate extension
      String fileName = metadata.get(TikaCoreProperties.RESOURCE_NAME_KEY);
      String extension = getFileExtension(fileName);
      tempFile = Files.createTempFile("docling_input", extension);

      // Copy input stream to temp file
      try (FileOutputStream fos = new FileOutputStream(tempFile.toFile())) {
        in.transferTo(fos);
      }

      // Execute docling Python script
      ProcessExecutor.ProcessResult result =
          ProcessExecutor.executePythonScript(
              DOCLING_SCRIPT_PATH, PROCESS_TIMEOUT_SECONDS, tempFile.toString(), outputFormat);

      if (!result.isSuccess()) {
        String errorMsg =
            String.format(
                "Docling process failed (exit code: %d, timed out: %s): %s",
                result.getExitCode(), result.isTimedOut(), result.getStderr());
        log.error(errorMsg);
        throw new TikaException(errorMsg);
      }

      // Parse the JSON output from the Python script
      DoclingResult doclingResult = parseDoclingOutput(result.getStdout());

      if (!doclingResult.isSuccess()) {
        String errorMsg = "Docling extraction failed: " + doclingResult.getError();
        log.error(errorMsg);
        throw new TikaException(errorMsg);
      }

      String extractedText = doclingResult.getText();
      if (extractedText == null || extractedText.trim().isEmpty()) {
        log.warn("Docling extracted empty text from file: {}", fileName);
        return "";
      }

      // Add metadata from docling result to Tika metadata
      addDoclingMetadata(metadata, doclingResult.getMetadata());

      log.info(
          "Successfully extracted {} characters using docling from file: {}",
          extractedText.length(),
          fileName);

      return extractedText;

    } catch (Exception e) {
      if (e instanceof TikaException) {
        throw e;
      }
      String errorMsg = "Unexpected error during docling extraction: " + e.getMessage();
      log.error(errorMsg, e);
      throw new TikaException(errorMsg, e);
    } finally {
      // Clean up temporary file
      if (tempFile != null) {
        try {
          Files.deleteIfExists(tempFile);
        } catch (IOException e) {
          log.warn("Failed to delete temporary file: {}", tempFile, e);
        }
      }
    }
  }

  /**
   * Parse the JSON output from the docling Python script.
   *
   * @param jsonOutput JSON output from the script
   * @return DoclingResult object
   * @throws IOException If JSON parsing fails
   */
  private DoclingResult parseDoclingOutput(String jsonOutput) throws IOException {
    try {
      return objectMapper.readValue(jsonOutput, DoclingResult.class);
    } catch (Exception e) {
      log.error("Failed to parse docling output JSON: {}", jsonOutput, e);
      throw new IOException("Failed to parse docling output: " + e.getMessage(), e);
    }
  }

  /**
   * Add metadata from docling result to Tika metadata.
   *
   * @param tikaMetadata Tika metadata object
   * @param doclingMetadata Docling metadata JSON node
   */
  private void addDoclingMetadata(Metadata tikaMetadata, JsonNode doclingMetadata) {
    if (doclingMetadata == null || !doclingMetadata.isObject()) {
      return;
    }

    try {
      // Add converter info
      tikaMetadata.set("docling:converter", "docling");

      // Add title if available
      if (doclingMetadata.has("title")) {
        String title = doclingMetadata.get("title").asText();
        if (title != null && !title.trim().isEmpty()) {
          tikaMetadata.set(TikaCoreProperties.TITLE, title);
        }
      }

      // Add page count if available
      if (doclingMetadata.has("page_count")) {
        int pageCount = doclingMetadata.get("page_count").asInt();
        tikaMetadata.set("docling:page_count", String.valueOf(pageCount));
      }

    } catch (Exception e) {
      log.warn("Error adding docling metadata to Tika metadata", e);
    }
  }

  /**
   * Get file extension from filename.
   *
   * @param fileName File name
   * @return File extension with dot (e.g., ".xls") or empty string if no extension
   */
  private String getFileExtension(String fileName) {
    if (fileName == null || fileName.trim().isEmpty()) {
      return "";
    }

    int lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
      return fileName.substring(lastDotIndex);
    }

    return "";
  }
}
