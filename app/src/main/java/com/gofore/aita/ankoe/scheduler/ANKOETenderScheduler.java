package com.gofore.aita.ankoe.scheduler;

import com.gofore.aita.ankoe.model.ANKOETender;
import com.gofore.aita.ankoe.model.TenderDetail;
import com.gofore.aita.ankoe.model.TenderFileInfos;
import com.gofore.aita.ankoe.model.TenderSearchRequest;
import com.gofore.aita.ankoe.service.ANKOEVergabeportalService;
import com.gofore.aita.extraction.domain.FileExtractionService;
import com.gofore.aita.tender.data.TenderRepository;
import com.gofore.aita.tender.domain.TenderService;
import com.gofore.aita.tender.domain.User;
import com.gofore.aita.tender.domain.models.Tender;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@EnableScheduling
@Slf4j
@Profile("!dev")
public class ANKOETenderScheduler {

  private final ANKOEVergabeportalService ankoeService;
  private final TenderRepository tenderRepository;
  private final FileExtractionService fileExtractionService;
  private final TenderService tenderService;
  private final User systemUser;

  @Autowired
  public ANKOETenderScheduler(
      ANKOEVergabeportalService ankoeService,
      TenderRepository tenderRepository,
      FileExtractionService fileExtractionService,
      TenderService tenderService) {
    this.ankoeService = ankoeService;
    this.tenderRepository = tenderRepository;
    this.fileExtractionService = fileExtractionService;
    this.tenderService = tenderService;
    this.systemUser = new User("system", "ANKOE Tender Scheduler");
  }

  /**
   * Scheduled job that runs every hour to fetch tenders from ANKOE Vergabeportal and downloads
   * their files.
   */
  @Scheduled(cron = "0 0/20 * * * ?")
  public void scheduledTenderProcessing() {
    log.info(
        "Starting scheduled ANKOE tender processing at {}",
        LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

    try {
      // Step 1: Call getTenders endpoint to receive tenders
      TenderSearchRequest searchRequest =
          ankoeService.createDefaultSearchRequest(1, 20, null, null, null, null, null, 60);
      List<ANKOETender> ankoeTenders = ankoeService.getTenders(searchRequest);

      for (ANKOETender ankoeTender : ankoeTenders) {
        // only handle tenders that are not already stored in the DB
        if (tenderRepository.existsByExternalId(ankoeTender.getId())) {
          log.info(
              "Tender {} with ID {} is already in DB, skipping",
              ankoeTender.getId(),
              ankoeTender.getTitle());
        } else {
          String tenderId = ankoeTender.getId();
          log.info("Processing tender: {} - {}", tenderId, ankoeTender.getTitle());

          // Step 2: Get details about the first tender
          TenderDetail tenderDetail = ankoeService.getTenderDetails(tenderId);

          // Step 3: Create and save the tender in our system
          Tender tender = ankoeService.mapToDomainTender(tenderDetail);
          tender.setCreationTime(OffsetDateTime.now().truncatedTo(ChronoUnit.SECONDS).toString());
          tender.setCreatedBy(systemUser);
          Tender storedTender = tenderRepository.save(tender);
          log.info("Saved tender {} with internal ID: {}", tenderId, storedTender.getId());

          // Step 4: Get file infos for the tender
          List<TenderFileInfos> fileInfos = ankoeService.getTenderFileInfos(tenderId);

          // Step 5: Download and add files to the tender
          ankoeService.downloadAllTenderFilesAndAddToTender(
              fileInfos, tenderId, storedTender.getId());

          // Step 6: get updated tender and trigger extraction (saves ExtractedDocument records)
          Tender updatedTender = tenderRepository.findById(storedTender.getId()).orElseThrow();
          fileExtractionService.processFilesForExtraction(updatedTender);

          // Step 7: use AI to extract missing tender fields, update tender and start analysis
          updatedTender = tenderRepository.findById(storedTender.getId()).orElseThrow();
          tenderService.createTenderWithAI(updatedTender, null, systemUser);

          log.info(
              "Completed processing tender {} with internal id: {}",
              tenderId,
              storedTender.getId());

          // break after processing first new tender
          // (to prevent exceeding token per minute limit of AI model)
          break;
        }
      }

    } catch (Exception e) {
      log.error("Error during scheduled tender processing: {}", e.getMessage(), e);
    }
  }
}
