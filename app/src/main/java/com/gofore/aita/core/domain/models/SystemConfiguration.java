package com.gofore.aita.core.domain.models;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = "config")
public class SystemConfiguration {
    /**
     * OS specific newline character(s)
     */
    private static final String N = System.lineSeparator();

    private static final String AI_ANALYSIS_SYSTEM_PROMPT = "You are an AI assistant that supports us in analysing tenders.";

    private static final String AI_ANALYSIS_ANALYSIS_PROMPT = "You will receive a description and documents of the tender." //
            + " " //
            + "Please try to generate a short report highlighting the most salient points.";

    private static final String AI_STRUCTURED_OUTPUT_PROMPT = "Du bist ein hochpr\u00E4ziser Ausschreibungs-Extraktionsdienst." //
            + " " //
            + "Analysiere den folgenden Text und liefere **ausschlie\u00DFlich** ein JSON-Objekt ohne Markdown, Code-Bl\u00F6cke oder zus\u00E4tzliche Felder." + //
            " " + "Verwende exakt diese Felder **in genau dieser Reihenfolge** und beachte die Beschreibungen:" + N + //
            N + //
            "**WICHTIG:**  " + N + //
            "• **Suche und extrahiere** jeden Wert aktiv aus dem Text.  " + N +//
            "• Verwende Platzhalter **erst**, wenn du trotz aufw\u00E4ndiger Pr\u00FCfung **keinen** plausiblen Wert finden kannst." + N +//
            N +//
            "1. `\"client\"`: string  " + N +//
            "   (Auftraggeber dieser Ausschreibung)  " + N +//
            "   • Falls nach sorgf\u00E4ltiger Pr\u00FCfung nichts extrahierbar → `\"PLATZHALTER_CLIENT\"`" + N +//
            N +//
            "2. `\"submissionDate\"`: string im ISO8601_DATETIME (`\"YYYY-MM-DDThh:mm:ss\"`)  " + N +//
            "   (Einreichungsfrist – Datum und Uhrzeit, bis zu der das vollst\u00E4ndige Angebot abgegeben sein muss)  " + N +//
            "   • Falls nach sorgf\u00E4ltiger Pr\u00FCfung nichts extrahierbar → `\"1970-01-01T00:00:00\"`" + N +//
            N +//
            "3. `\"bindingDeadline\"`: string im ISO8601_DATE (`\"YYYY-MM-DD\"`)  " + N +//
            "   (Bindefrist – Zeitraum, in dem eine Angebotseinreichung verbindlich ist)  " + N +//
            "    • Falls nach sorgf\u00E4ltiger Pr\u00FCfung nichts extrahierbar → `\"1970-01-01\"`" + N +//
            N +//
            "4. `\"contractDuration\"`: string  " + N +//
            "   (Vertragslaufzeit – erwartete Dauer des Vertrags bei Zuschlag)  " + N +//
            "   • Falls nach sorgf\u00E4ltiger Pr\u00FCfung nichts extrahierbar → `\"PLATZHALTER_CONTRACT_DURATION\"`" + N +//
            N +//
            "5. `\"publicationDate\"`: string im ISO8601_DATE (`\"YYYY-MM-DD\"`)  " + N +//
            "   (Ver\u00F6ffentlichungsdatum der Ausschreibung)  " + N +//
            "   • Falls nach sorgf\u00E4ltiger Pr\u00FCfung nichts extrahierbar → `\"1970-01-01\"`" + N +//
            N +//
            "6. `\"questionDeadline\"`: string im ISO8601_DATETIME (`\"YYYY-MM-DDThh:mm:ss\"`)  " + N +//
            "   (Fragenfrist – letzte Frist f\u00FCr R\u00FCckfragen zur Ausschreibung)  " + N +//
            "   • Falls nach sorgf\u00E4ltiger Pr\u00FCfung nichts extrahierbar → `\"1970-01-01T00:00:00\"`" + N +//
            N +//
            "7. `\"contractValue\"`: number  " + N +//
            "   (maximaler Auftragswert in Euro)  " + N +//
            "   • Falls nach sorgf\u00E4ltiger Pr\u00FCfung nichts extrahierbar → `-1`" + N +//
            N +//
            "8. `\"maximumBudget\"`: string  " + N +//
            "   (H\u00F6chstbudget in Personentagen, z. B. \"PT100\")  " + N +//
            "   • Falls nach sorgf\u00E4ltiger Pr\u00FCfung nichts extrahierbar → `\"PLATZHALTER_MAXIMUM_BUDGET\"`" + N +//
            N +//
            "9. `\"winningCriteria\"`: string  " + N +//
            "   (Vergabekriterien – Liste der wichtigsten Entscheidungskriterien, um diese Ausschreibung zu gewinnen)  " + N +//
            "   • Falls nach sorgf\u00E4ltiger Pr\u00FCfung nichts extrahierbar → `\"PLATZHALTER_WINNING_CRITERIA\"`" + N +//
            N +//
            "10. `\"weightingPriceQuality\"`: string  " + N +//
            "    (Gewichtung Preis vs. Qualit\u00E4t, z. B. \"70 % Preis / 30 % Qualit\u00E4t\")  " + N +//
            "    • Falls nach sorgf\u00E4ltiger Pr\u00FCfung nichts extrahierbar → `\"PLATZHALTER_WEIGHTING\"`" + N +//
            N +//
            "11. `\"deliveryLocation\"`: string  " + N +//
            "    (Liefer- oder Leistungsort, Ort der Erf\u00FCllung)  " + N +//
            "    • Falls nach sorgf\u00E4ltiger Pr\u00FCfung nichts extrahierbar → `\"PLATZHALTER_DELIVERY_LOCATION\"`" + N +//
            N +//
            "12. `\"rating\"`: integer (1–5)  " + N +//
            "    (Einsch\u00E4tzung der Interessenqualit\u00E4t basierend auf folgenden Kriterien: " +//
            "1 ist niedriges Rating, 5 das beste, " + //
            "relevante Fakten: Vergabequalit\u00E4t --> je h\u00F6her Qualit\u00E4t gegen\u00FCber Preis gewertet wird, desto besser;" +  //
            " " + "Remote Arbeit muss m\u00F6glich sein, ausser die Standorte der Erf\u00FCllung sind Wien, Salzburg, M\u00FCnchen, Stuttgart, Braunschweig;" + //
            " " + "wenn Nutzung moderner Technologien wichtig sind, erh\u00F6ht das das Rating;" + //
            " " + "je gr\u00F6\u00DFer Gesamtsumme und Laufzeit der Beauftragung sind, desto besser)  " + N +//
            "    • Falls nach sorgf\u00E4ltiger Pr\u00FCfung nichts extrahierbar → `-1`" + N +//
            N +//
            "**Zus\u00E4tzliche Regeln:**" + N +//
            "- **Keine** Felder d\u00FCrfen fehlen oder `null` sein." + N +//
            "- Datums- und Zeitangaben **immer** ISO 8601-konform und entsprechend der Vorgabe nur Datum oder Datum und Zeit." + N +//
            "- Numerische Felder **ohne** W\u00E4hrungssymbole oder Einheiten." + N +//
            "- Platzhalter-Strings **immer** in Gro\u00DFbuchstaben mit Pr\u00E4fix `PLATZHALTER_`." + N +//
            "- Gib **nur** das rohe JSON-Objekt aus, **keine** einleitende oder abschlie\u00DFende Prosa." + N;


    private static final String AI_COMBINE_EXTRACTION_STRUCTURED_OUTPUT_PROMPT = "You are a tender-extraction service. You have already extracted multiple times information from documents and returned JSON objects. Now accumulate the results in a single JSON object. If one JSON object contains a placeholder value, use the value from other JSON objects if available." + N + //
            "Output **only** one single JSON object (no markdown, no code fences, no extra commentary) with exactly these fields and in this order:" + N + //
            "  client (issuer of this tender offer): string | PLATZHALTER_CLIENT," + N + //
            "  submissionDate (Exact date by which the entire delivery package must be delivered.): string | 1970-01-01T00:00:00," + N + //
            "  bindingDeadline (The binding deadline is the period during which a bidder is legally bound by their offer. Within this period, the offer cannot be modified or withdrawn.): string | 1970-01-01," + N + //
            "  contractDuration (How long the tender offer will last if contract awarded.): string | PLATZHALTER_CONTRACT_DURATION," + N + //
            "  publicationDate: string | 1970-01-01," + N + //
            "  questionDeadline: string | 1970-01-01T00:00:00," + N + //
            "  contractValue: number | -1," + N + //
            "  maximumBudget (The maximum amount listed in person-days (PT).): string | PLATZHALTER_MAXIMUM_BUDGET," + N + //
            "  winningCriteria (List of criteria that are especially crucial for awarding the contract.): string | PLATZHALTER_WINNING_CRITERIA," + N + //
            "  weightingPriceQuality (Weighting of price versus quality.): string | PLATZHALTER_WEIGHTING," + N + //
            "  deliveryLocation: string | PLATZHALTER_DELIVERY_LOCATION" + N + //
            "  rating: number 1-5 (Assessment of the quality of the interest based on the following criteria: 1 is low rating, 5 is the best, relevant facts: Award quality --> the higher quality is rated compared to price, the better; remote work must be possible, unless the fulfillment locations are Vienna, Salzburg, Munich, Stuttgart, Braunschweig; if the use of modern technologies is important, this increases the rating; the larger the total amount and duration of the contract, the better)| -1" + N + //
            N + //
            "**Important**:" + N + //
            "- Every field in the JSON responses contains either a real value or a placeholder value. **Only** use the placeholder value in the final output if you cannot find a real value in any of the JSON objects." + N + //
            "- You can see the placeholder values of the fields behind the '|' symbol." + N + //
            N + //
            "Field requirements:" + N + //
            "- Dates must use ISO 8601 (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS)" + N + //
            "- Numeric fields (contractValue) should be plain numbers (no currency symbols)." + N + //
            "- **No** fields should be null or missing.";

    private static final String AI_COMBINE_ANALYSIS_PROMPT = "You are a tender-analysis service. You have already analyzed multiple times information from documents and returned formatted text. Now **accumulate** all those previous results by combining all the information in a single text which has the same format (tables, markdown, etc.) as the previous analysis results." + N + //
            "**Important:**" + N + //
            "- If one analysis contains placeholders (rows in the tables), use the values from the other analysis if available." + N + //
            "- **Don't lose** any information of the original analysis results (except for the placeholders)!" + N + //
            "- The previous results contain tables. Preserve whole columns, **don't mix fields from different columns**! So if there is a table with non-placeholder values, add the whole column to the final results table." + N + //
            "- If you cannot find a value in one analysis result, use the value from the other analysis result if available." + N + //
            "- Just combine the given results and don't add any additional commentary." + N + //
            "- For duplicate information keep the information only once (even if it is in different language or phrased a little bit differently)." + N + //
            "- The next message contains the prompt that has been used for the previous analysis so you can understand the context and the format which the combined result should have. Afterwards you will receive the analysis results which you have to combine.";

    @Id
    private String id;
    private String aiAnalysisSystemPrompt;
    private String aiAnalysisAnalysisPrompt;
    private String aiStructuredOutputPrompt;
    private String aiCombineExtractedTenderFieldsPrompt;
    private String aiCombineAnalysisTenderFieldsPrompt;

    public static SystemConfiguration defaultConfiguration() {
        SystemConfiguration systemConfiguration = new SystemConfiguration();
        systemConfiguration.setAiAnalysisSystemPrompt(AI_ANALYSIS_SYSTEM_PROMPT);
        systemConfiguration.setAiAnalysisAnalysisPrompt(AI_ANALYSIS_ANALYSIS_PROMPT);
        systemConfiguration.setAiStructuredOutputPrompt(AI_STRUCTURED_OUTPUT_PROMPT);
        systemConfiguration.setAiCombineExtractedTenderFieldsPrompt(AI_COMBINE_EXTRACTION_STRUCTURED_OUTPUT_PROMPT);
        systemConfiguration.setAiCombineAnalysisTenderFieldsPrompt(AI_COMBINE_ANALYSIS_PROMPT);
        return systemConfiguration;
    }
}
