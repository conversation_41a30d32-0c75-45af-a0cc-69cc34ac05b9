package com.gofore.aita.tender.api;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.gofore.aita.BaseTest;
import com.gofore.aita.ankoe.controller.ANKOEVergabeportalApiController;
import com.gofore.aita.ankoe.service.ANKOEVergabeportalAuthService;
import com.gofore.aita.ankoe.service.ANKOEVergabeportalService;
import com.gofore.aita.tender.api.dto.*;
import com.gofore.aita.tender.api.mapper.TenderCreateSimpleMapper;
import com.gofore.aita.tender.api.mapper.TenderMapper;
import com.gofore.aita.tender.domain.TenderService;
import com.gofore.aita.tender.domain.User;
import com.gofore.aita.tender.domain.mapper.UserMapper;
import com.gofore.aita.tender.domain.models.FileMetadata;
import com.gofore.aita.tender.domain.models.Tender;
import com.gofore.aita.tender.domain.models.TenderStatus;
import com.gofore.aita.utils.TestUtils;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.util.MultiValueMap;

public class TenderControllerTest extends BaseTest {

  @Autowired private MockMvc mockMvc;

  @MockitoBean private TenderService tenderService;
  @MockitoBean private TenderMapper tenderMapper;
  @MockitoBean private TenderCreateSimpleMapper tenderCreateSimpleMapper;
  @MockitoBean private UserMapper userMapper;
  @MockitoBean private com.gofore.aita.tender.api.mapper.TenderUpdateMapper tenderUpdateMapper;
  @MockitoBean private com.gofore.aita.tender.api.mapper.FileMetadataMapper fileMetadataMapper;
  @MockitoBean private com.gofore.aita.tender.api.mapper.AnalysisResultMapper analysisResultMapper;
  @MockitoBean private ANKOEVergabeportalApiController ankoeVergabeportalApiController;
  @MockitoBean private ANKOEVergabeportalService ankoeVergabeportalService;
  @MockitoBean private ANKOEVergabeportalAuthService ankoeVergabeportalAuthService;

  /** Test getting all tenders with pagination and sorting. */
  @Test
  void testGetAllTenders() throws Exception {
    // Arrange
    Tender tender1 = new Tender();
    tender1.setId("1");
    tender1.setCreationTime("2020-01-01T10:00:00Z"); // youngest

    Tender tender2 = new Tender();
    tender2.setId("2");
    tender2.setCreationTime("2020-01-01T12:00:00Z"); // oldest

    Tender tender3 = new Tender();
    tender3.setId("3");
    tender3.setCreationTime("2020-01-01T11:00:00Z"); // middle

    Pageable expectedPageable =
        PageRequest.of(
            0,
            10,
            Sort.by(Sort.Direction.DESC, TenderSortableFieldsDTO.CREATION_TIME.getFieldName()));

    List<Tender> sortedTenders = List.of(tender2, tender3, tender1);
    Page<Tender> tenderPage = new PageImpl<>(sortedTenders, expectedPageable, sortedTenders.size());

    when(tenderService.getAll(expectedPageable)).thenReturn(tenderPage);
    when(tenderService.findAllTendersWithFilter(eq(expectedPageable), any(), any()))
        .thenReturn(tenderPage);

    // Mock the mapper to convert Tender to TenderDTO
    TenderDTO tenderDTO1 = new TenderDTO();
    tenderDTO1.setId("1");
    TenderDTO tenderDTO2 = new TenderDTO();
    tenderDTO2.setId("2");
    TenderDTO tenderDTO3 = new TenderDTO();
    tenderDTO3.setId("3");

    when(tenderMapper.toTenderDTO(tender1)).thenReturn(tenderDTO1);
    when(tenderMapper.toTenderDTO(tender2)).thenReturn(tenderDTO2);
    when(tenderMapper.toTenderDTO(tender3)).thenReturn(tenderDTO3);

    // Act & Assert
    mvc.perform(
            get("/tenders")
                .param("page", "0")
                .param("size", "10")
                .param("workflowStatuses", "New")
                .param("searchString", "")
                .param("sortBy", "creationTime")
                .param("sortDirection", "desc")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andDo(
            result -> {
              String json = result.getResponse().getContentAsString();
              TenderPagedDTO response = objectMapper.readValue(json, TenderPagedDTO.class);

              assertEquals(3, response.getPaging().getTotalElements());
              assertEquals("2", response.getData().get(0).getId());
              assertEquals("3", response.getData().get(1).getId());
            });
  }

  /** Test creating a new tender with file upload. */
  @Test
  public void testCreateTenderWithFile() throws Exception {
    // Arrange
    HttpHeaders headers = TestUtils.createTestHttpHeaders();
    MultiValueMap<String, String> params = TestUtils.createTenderParams();
    MockMultipartFile file =
        TestUtils.createMockFile("test-document.txt", "This is a test document");

    Tender tender = new Tender();
    tender.setId("test-id");
    tender.setTitle(params.getFirst("title"));
    tender.setDescription(params.getFirst("description"));

    FileMetadata fileMetadata =
        new FileMetadata(
            "file-id",
            file.getOriginalFilename(),
            "test-document.txt",
            "test-id/test-document.txt",
            file.getSize());
    tender.setFiles(List.of(fileMetadata));

    when(tenderService.save(any(), any(), any(User.class))).thenReturn(tender);

    // Mock the mappers
    TenderDTO tenderDTO = new TenderDTO();
    tenderDTO.setId(tender.getId());
    tenderDTO.setTitle(tender.getTitle());
    tenderDTO.setDescription(tender.getDescription());

    FileMetadataDTO fileDTO = new FileMetadataDTO();
    fileDTO.setId(fileMetadata.getId());
    fileDTO.setFileName(fileMetadata.getFileName());
    fileDTO.setFileSizeBytes(fileMetadata.getFileSizeBytes());
    tenderDTO.setFiles(List.of(fileDTO));

    when(tenderMapper.toTender(any(TenderCreateDTO.class))).thenReturn(new Tender());
    when(tenderMapper.toTenderDTO(any(Tender.class))).thenReturn(tenderDTO);
    when(userMapper.toUser(any())).thenReturn(new User("user-id", "Test User"));

    // Act & Assert
    mvc.perform(multipart("/tenders").file(file).params(params).headers(headers))
        .andExpect(status().isCreated())
        .andDo(
            result -> {
              String json = result.getResponse().getContentAsString();
              TenderDTO response = objectMapper.readValue(json, TenderDTO.class);

              assertEquals("test-id", response.getId());
              assertEquals(params.getFirst("title"), response.getTitle());
              assertEquals(params.getFirst("description"), response.getDescription());

              assertNotNull(response.getFiles());
              assertEquals(1, response.getFiles().size());
              FileMetadataDTO responseFileDTO = response.getFiles().get(0);
              assertEquals(file.getOriginalFilename(), responseFileDTO.getFileName());
              assertEquals(file.getSize(), responseFileDTO.getFileSizeBytes());
            });
  }

  @Test
  void createTenderWithAI_ShouldReturnCreatedTender() throws Exception {
    // Arrange
    Tender mockTender = new Tender();
    mockTender.setId("test-id");
    mockTender.setTitle("Test Tender");
    mockTender.setSourceUrl("https://example.com");
    mockTender.setDescription("Test description");
    mockTender.setStatus(TenderStatus.NEW);
    mockTender.setClient("Mock Client Organization");

    User mockUser = new User("test-user-id", "Test User");

    when(tenderCreateSimpleMapper.toTender(any())).thenReturn(mockTender);
    when(userMapper.toUser(any())).thenReturn(mockUser);
    when(tenderService.createTenderWithAI(any(Tender.class), anyList(), any(User.class)))
        .thenReturn(mockTender);
    when(tenderMapper.toTenderDTO(any())).thenReturn(null); // We'll just check the status

    MockMultipartFile file =
        new MockMultipartFile(
            "files", "test.pdf", MediaType.APPLICATION_PDF_VALUE, "test content".getBytes());

    // Act & Assert
    mockMvc
        .perform(
            multipart("/tenders/create-with-ai")
                .file(file)
                .param("title", "Test Tender")
                .param("sourceUrl", "https://example.com")
                .param("description", "Test description")
                .header("Authorization", "Bearer test-token"))
        .andExpect(status().isCreated());
  }
}
