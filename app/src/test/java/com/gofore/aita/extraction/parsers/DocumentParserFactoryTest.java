package com.gofore.aita.extraction.parsers;

import static org.junit.jupiter.api.Assertions.*;

import com.gofore.aita.extraction.api.IDocumentParser;
import org.junit.jupiter.api.Test;

class DocumentParserFactoryTest {

  @Test
  void getParser_PdfExtension_ReturnsDoclingParser() {
    // Act
    IDocumentParser parser = DocumentParserFactory.getParser("pdf");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser,
        "PDF files should now use DoclingParser with PdfParser as fallback");
  }

  @Test
  void getParser_DocExtension_ReturnsDoclingParser() {
    // Act
    IDocumentParser parser = DocumentParserFactory.getParser("doc");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser,
        "DOC files should now use DoclingParser with OfficeParser as fallback");
  }

  @Test
  void getParser_DocxExtension_ReturnsDoclingParser() {
    // Act
    IDocumentParser parser = DocumentParserFactory.getParser("docx");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser,
        "DOCX files should now use DoclingParser with OfficeParser as fallback");
  }

  @Test
  void getParser_PptExtension_ReturnsDoclingParser() {
    // Act
    IDocumentParser parser = DocumentParserFactory.getParser("ppt");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser,
        "PPT files should now use DoclingParser with OfficeParser as fallback");
  }

  @Test
  void getParser_PptxExtension_ReturnsDoclingParser() {
    // Act
    IDocumentParser parser = DocumentParserFactory.getParser("pptx");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser,
        "PPTX files should now use DoclingParser with OfficeParser as fallback");
  }

  @Test
  void getParser_XlsExtension_ReturnsDoclingParser() {
    // Act
    IDocumentParser parser = DocumentParserFactory.getParser("xls");

    // Assert
    assertNotNull(parser);
    assertTrue(
        parser instanceof DoclingParser,
        "XLS files should now use DoclingParser instead of ExcelParser");
  }

  @Test
  void getParser_XlsxExtension_ReturnsDoclingParser() {
    // Act
    IDocumentParser parser = DocumentParserFactory.getParser("xlsx");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser,
        "XLSX files should now use DoclingParser with ExcelParser as fallback");
  }

  @Test
  void getParser_TxtExtension_ReturnsDoclingParser() {
    // Act
    IDocumentParser parser = DocumentParserFactory.getParser("txt");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser,
        "TXT files should now use DoclingParser with PlainTextParser as fallback");
  }

  @Test
  void getParser_CsvExtension_ReturnsDoclingParser() {
    // Act
    IDocumentParser parser = DocumentParserFactory.getParser("csv");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser,
        "CSV files should now use DoclingParser with PlainTextParser as fallback");
  }

  @Test
  void getParser_UnknownExtension_ReturnsDoclingParser() {
    // Act
    IDocumentParser parser = DocumentParserFactory.getParser("unknown");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser,
        "Unknown extensions should use DoclingParser with AutoDetectDefaultParser as fallback");
  }

  @Test
  void getParser_NullExtension_ReturnsDoclingParser() {
    // Act
    IDocumentParser parser = DocumentParserFactory.getParser(null);

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser,
        "Null extension should use DoclingParser with AutoDetectDefaultParser as fallback");
  }

  @Test
  void getParser_EmptyExtension_ReturnsDoclingParser() {
    // Act
    IDocumentParser parser = DocumentParserFactory.getParser("");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser,
        "Empty extension should use DoclingParser with AutoDetectDefaultParser as fallback");
  }

  @Test
  void getParser_WhitespaceExtension_ReturnsDoclingParser() {
    // Act
    IDocumentParser parser = DocumentParserFactory.getParser("   ");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser,
        "Whitespace extension should use DoclingParser with AutoDetectDefaultParser as fallback");
  }

  @Test
  void getParser_UppercaseExtension_ReturnsCorrectParser() {
    // Act
    IDocumentParser pdfParser = DocumentParserFactory.getParser("PDF");
    IDocumentParser xlsParser = DocumentParserFactory.getParser("XLS");
    IDocumentParser xlsxParser = DocumentParserFactory.getParser("XLSX");

    // Assert
    assertTrue(pdfParser instanceof DoclingParser, "PDF should use DoclingParser");
    assertTrue(xlsParser instanceof DoclingParser, "XLS should use DoclingParser");
    assertTrue(xlsxParser instanceof DoclingParser, "XLSX should use DoclingParser");
  }

  @Test
  void getParser_MixedCaseExtension_ReturnsCorrectParser() {
    // Act
    IDocumentParser pdfParser = DocumentParserFactory.getParser("PdF");
    IDocumentParser xlsParser = DocumentParserFactory.getParser("XlS");
    IDocumentParser xlsxParser = DocumentParserFactory.getParser("XlSx");

    // Assert
    assertTrue(pdfParser instanceof DoclingParser, "PDF should use DoclingParser");
    assertTrue(xlsParser instanceof DoclingParser, "XLS should use DoclingParser");
    assertTrue(xlsxParser instanceof DoclingParser, "XLSX should use DoclingParser");
  }

  @Test
  void extensionFrom_ValidFileName_ReturnsExtension() {
    // Act & Assert
    assertEquals("pdf", DocumentParserFactory.extensionFrom("document.pdf"));
    assertEquals("xls", DocumentParserFactory.extensionFrom("spreadsheet.xls"));
    assertEquals("xlsx", DocumentParserFactory.extensionFrom("workbook.xlsx"));
    assertEquals("txt", DocumentParserFactory.extensionFrom("readme.txt"));
  }

  @Test
  void extensionFrom_FileNameWithMultipleDots_ReturnsLastExtension() {
    // Act & Assert
    assertEquals("pdf", DocumentParserFactory.extensionFrom("my.document.with.dots.pdf"));
    assertEquals("backup", DocumentParserFactory.extensionFrom("file.xls.backup"));
  }

  @Test
  void extensionFrom_FileNameWithoutExtension_ReturnsEmptyString() {
    // Act & Assert
    assertEquals("", DocumentParserFactory.extensionFrom("filename"));
    assertEquals("", DocumentParserFactory.extensionFrom("filename."));
  }

  @Test
  void extensionFrom_NullFileName_ReturnsNull() {
    // Act & Assert
    assertNull(DocumentParserFactory.extensionFrom(null));
  }

  @Test
  void extensionFrom_EmptyFileName_ReturnsEmptyString() {
    // Act & Assert
    assertEquals("", DocumentParserFactory.extensionFrom(""));
  }
}
