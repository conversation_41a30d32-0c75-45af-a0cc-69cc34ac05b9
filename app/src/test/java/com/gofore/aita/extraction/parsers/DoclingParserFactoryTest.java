package com.gofore.aita.extraction.parsers;

import static org.junit.jupiter.api.Assertions.*;

import com.gofore.aita.extraction.api.IDocumentParser;
import org.junit.jupiter.api.Test;

class DoclingParserFactoryTest {

  @Test
  void createDoclingParser_PdfExtension_ReturnsDoclingParserWithPdfFallback() {
    // Act
    IDocumentParser parser = DoclingParserFactory.createDoclingParser("pdf");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser);
  }

  @Test
  void createDoclingParser_DocExtension_ReturnsDoclingParserWithOfficeFallback() {
    // Act
    IDocumentParser parser = DoclingParserFactory.createDoclingParser("doc");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser);
  }

  @Test
  void createDoclingParser_XlsExtension_ReturnsDoclingParserWithExcelFallback() {
    // Act
    IDocumentParser parser = DoclingParserFactory.createDoclingParser("xls");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser);
  }

  @Test
  void createDoclingParser_TxtExtension_ReturnsDoclingParserWithPlainTextFallback() {
    // Act
    IDocumentParser parser = DoclingParserFactory.createDoclingParser("txt");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser);
  }

  @Test
  void createDoclingParser_UnknownExtension_ReturnsDoclingParserWithAutoDetectFallback() {
    // Act
    IDocumentParser parser = DoclingParserFactory.createDoclingParser("unknown");

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser);
  }

  @Test
  void createDoclingParser_NullExtension_ReturnsDoclingParserWithAutoDetectFallback() {
    // Act
    IDocumentParser parser = DoclingParserFactory.createDoclingParser(null);

    // Assert
    assertNotNull(parser);
    assertTrue(parser instanceof DoclingParser);
  }

  @Test
  void shouldUseDocling_AnyExtension_ReturnsTrue() {
    // Act & Assert
    assertTrue(DoclingParserFactory.shouldUseDocling("pdf"));
    assertTrue(DoclingParserFactory.shouldUseDocling("doc"));
    assertTrue(DoclingParserFactory.shouldUseDocling("unknown"));
    assertTrue(DoclingParserFactory.shouldUseDocling(null));
  }

  @Test
  void getOptimalDoclingExtensions_ReturnsExpectedExtensions() {
    // Act
    String[] extensions = DoclingParserFactory.getOptimalDoclingExtensions();

    // Assert
    assertNotNull(extensions);
    assertTrue(extensions.length > 0);
    
    // Check for some expected extensions
    boolean containsPdf = false;
    boolean containsDoc = false;
    boolean containsXls = false;
    
    for (String ext : extensions) {
      if ("pdf".equals(ext)) containsPdf = true;
      if ("doc".equals(ext)) containsDoc = true;
      if ("xls".equals(ext)) containsXls = true;
    }
    
    assertTrue(containsPdf, "Should contain PDF extension");
    assertTrue(containsDoc, "Should contain DOC extension");
    assertTrue(containsXls, "Should contain XLS extension");
  }

  @Test
  void isOptimalForDocling_KnownExtensions_ReturnsTrue() {
    // Act & Assert
    assertTrue(DoclingParserFactory.isOptimalForDocling("pdf"));
    assertTrue(DoclingParserFactory.isOptimalForDocling("doc"));
    assertTrue(DoclingParserFactory.isOptimalForDocling("docx"));
    assertTrue(DoclingParserFactory.isOptimalForDocling("xls"));
    assertTrue(DoclingParserFactory.isOptimalForDocling("xlsx"));
    assertTrue(DoclingParserFactory.isOptimalForDocling("txt"));
  }

  @Test
  void isOptimalForDocling_UnknownExtension_ReturnsFalse() {
    // Act & Assert
    assertFalse(DoclingParserFactory.isOptimalForDocling("unknown"));
    assertFalse(DoclingParserFactory.isOptimalForDocling("xyz"));
  }

  @Test
  void isOptimalForDocling_NullExtension_ReturnsFalse() {
    // Act & Assert
    assertFalse(DoclingParserFactory.isOptimalForDocling(null));
  }

  @Test
  void isOptimalForDocling_EmptyExtension_ReturnsFalse() {
    // Act & Assert
    assertFalse(DoclingParserFactory.isOptimalForDocling(""));
    assertFalse(DoclingParserFactory.isOptimalForDocling("   "));
  }

  @Test
  void isOptimalForDocling_CaseInsensitive_ReturnsTrue() {
    // Act & Assert
    assertTrue(DoclingParserFactory.isOptimalForDocling("PDF"));
    assertTrue(DoclingParserFactory.isOptimalForDocling("Doc"));
    assertTrue(DoclingParserFactory.isOptimalForDocling("XLS"));
    assertTrue(DoclingParserFactory.isOptimalForDocling("TXT"));
  }
}
