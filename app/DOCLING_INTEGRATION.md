# Docling Integration for Document Parsing

This document describes the integration of IBM's Docling library for enhanced document text extraction across **all supported file formats**.

## Overview

The application now uses Docling (via Python process execution) as the **primary parser for all document formats** (PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX, TXT, CSV) with intelligent fallback to traditional parsers. This provides superior text extraction capabilities while maintaining full backward compatibility.

## Architecture

### Components

1. **DoclingParser** (`com.gofore.aita.extraction.parsers.DoclingParser`)
   - Primary parser implementing `IDocumentParser` interface
   - Handles **all document formats** using Docling via Python process execution
   - Supports both text and XHTML output formats
   - Intelligent fallback to traditional parsers when Docling fails
   - Constructor-based fallback configuration for different file types

2. **DoclingParserFactory** (`com.gofore.aita.extraction.parsers.DoclingParserFactory`)
   - Factory for creating DoclingParser instances with appropriate fallback parsers
   - Maps file extensions to correct fallback parsers (PdfParser, OfficeParser, ExcelParser, PlainTextParser)
   - Provides utility methods for checking Docling compatibility

3. **ProcessExecutor** (`com.gofore.aita.extraction.utils.ProcessExecutor`)
   - Utility class for executing external Python processes
   - Handles process timeouts, output capture, and error handling
   - Provides methods to check Python and package availability

4. **Python Script** (`docling_extractor.py`)
   - Standalone Python script that uses Docling library
   - Accepts file path and output format as arguments
   - Returns JSON with extraction results
   - Optimized for all document formats

5. **DocumentParserFactory** (`com.gofore.aita.extraction.parsers.DocumentParserFactory`)
   - Updated to use DoclingParser as primary for **all file formats**
   - Maintains full backward compatibility through fallback mechanism

### File Type Mapping (Updated)

- **PDF files**: `DoclingParser` with `PdfParser` fallback
- **DOC/DOCX files**: `DoclingParser` with `OfficeParser` fallback
- **PPT/PPTX files**: `DoclingParser` with `OfficeParser` fallback
- **XLS/XLSX files**: `DoclingParser` with `ExcelParser` fallback
- **TXT/CSV files**: `DoclingParser` with `PlainTextParser` fallback
- **Unknown formats**: `DoclingParser` with `AutoDetectDefaultParser` fallback

## Installation Requirements

### Python Dependencies

Install the required Python packages:

```bash
pip install -r requirements.txt
```

The main dependency is:
- `docling>=1.0.0` - IBM's document processing library

### Docker Support

The Docker image has been updated to include:
- Python 3 and pip
- Docling package installation
- The Python extraction script

## Usage

The integration is transparent to existing code. When **any supported document** is processed:

1. `DocumentParserFactory.getParser(extension)` returns a `DoclingParser` instance with appropriate fallback
2. `DoclingParser` buffers the input stream for potential reuse
3. **Primary attempt**: Creates temporary file and executes Python script with Docling
4. **Fallback on failure**: Uses traditional parser (PdfParser, OfficeParser, ExcelParser, PlainTextParser)
5. Parses the response and returns the extracted text
6. Cleans up temporary files

### Error Handling & Fallback Strategy

The parser provides robust error handling with intelligent fallback:

**Docling Failures (triggers fallback)**:
- Python not available on the system → Falls back to traditional parser
- Docling package not installed → Falls back to traditional parser
- Process execution failures or timeouts → Falls back to traditional parser
- Invalid JSON responses from Python script → Falls back to traditional parser
- Empty or malformed extraction results → Falls back to traditional parser

**Fallback Parser Mapping**:
- PDF files → PdfParser
- DOC/DOCX/PPT/PPTX files → OfficeParser
- XLS/XLSX files → ExcelParser
- TXT/CSV files → PlainTextParser
- Unknown formats → AutoDetectDefaultParser

**Complete Failure**: Only occurs when both Docling AND the fallback parser fail
- File I/O errors

## Configuration

### Timeouts

- Default process timeout: 300 seconds (5 minutes)
- Can be adjusted in `DoclingParser.PROCESS_TIMEOUT_SECONDS`

### Python Command

- Default Python command: `python3`
- Can be modified in `ProcessExecutor.PYTHON_COMMAND`

## Testing

### Unit Tests

- **DocumentParserFactoryTest**: Verifies correct parser selection for different file types
- **ProcessExecutorTest**: Tests process execution functionality
- **DoclingParserTest**: Tests the DoclingParser with mocked process execution

### Integration Testing

For full integration testing with actual Docling:

1. Install Python and Docling on the test system
2. Enable the integration test in `DoclingParserTest.parseToText_RealIntegration_WithMockFile()`
3. Run tests with: `mvn test -Dtest=DoclingParserTest`

## Monitoring and Logging

The integration provides comprehensive logging:

- **INFO**: Successful extractions with character counts
- **WARN**: Missing dependencies, empty extractions
- **ERROR**: Process failures, parsing errors, unexpected exceptions

Log messages include:
- File names being processed
- Process execution results
- Error details and stack traces

## Performance Considerations

- **Process Overhead**: Each extraction spawns a new Python process
- **Temporary Files**: Creates temporary files for each document
- **Memory Usage**: Python process memory is separate from JVM
- **Timeouts**: Long-running extractions are terminated after 5 minutes

## Fallback Strategy

If Docling is not available:
- The parser throws a `TikaException` with a clear error message
- The application can fall back to the `AutoDetectDefaultParser`
- Consider implementing a fallback to `ExcelParser` for XLS files if needed

## Future Enhancements

Potential improvements:
1. **Process Pooling**: Reuse Python processes to reduce startup overhead
2. **Streaming**: Process large files without creating temporary files
3. **Configuration**: Make timeouts and commands configurable via application properties
4. **Caching**: Cache extraction results for identical files
5. **Metrics**: Add metrics for process execution times and success rates

## Troubleshooting

### Common Issues

1. **"Python is not available"**
   - Ensure Python 3 is installed and in PATH
   - Verify with: `python3 --version`

2. **"Docling Python package is not installed"**
   - Install with: `pip install docling`
   - Verify with: `python3 -c "import docling"`

3. **Process timeouts**
   - Check file size and complexity
   - Increase timeout if needed
   - Monitor system resources

4. **Permission errors**
   - Ensure write permissions for temporary directory
   - Check Python script execution permissions

### Debug Mode

Enable debug logging for detailed process execution information:
```properties
logging.level.com.gofore.aita.extraction.utils.ProcessExecutor=DEBUG
logging.level.com.gofore.aita.extraction.parsers.DoclingParser=DEBUG
```

## Security Considerations

- **Process Execution**: Executes external Python processes
- **Temporary Files**: Creates temporary files with document content
- **Input Validation**: Validates file paths and arguments
- **Resource Limits**: Implements timeouts to prevent resource exhaustion

Ensure proper security measures in production environments.
