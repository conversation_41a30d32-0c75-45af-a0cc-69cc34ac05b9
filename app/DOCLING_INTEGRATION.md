# Docling Integration for Document Parsing

This document describes the integration of IBM's Docling library for enhanced document text extraction, specifically optimized for XLS files.

## Overview

The application now uses Docling (via Python process execution) to extract text from XLS files instead of the previous ExcelParser. This provides better text extraction capabilities and supports various document formats.

## Architecture

### Components

1. **DoclingParser** (`com.gofore.aita.extraction.parsers.DoclingParser`)
   - Implements `IDocumentParser` interface
   - Handles XLS files using Docling via Python process execution
   - Supports both text and XHTML output formats

2. **ProcessExecutor** (`com.gofore.aita.extraction.utils.ProcessExecutor`)
   - Utility class for executing external Python processes
   - Handles process timeouts, output capture, and error handling
   - Provides methods to check Python and package availability

3. **Python Script** (`docling_extractor.py`)
   - Standalone Python script that uses Docling library
   - Accepts file path and output format as arguments
   - Returns JSON with extraction results

### File Type Mapping

- **XLS files**: Now use `DoclingParser` (changed from `ExcelParser`)
- **XLSX files**: Still use `ExcelParser` (unchanged)
- **Other formats**: Unchanged (PDF, DOC, DOCX, PPT, PPTX, TXT, CSV)

## Installation Requirements

### Python Dependencies

Install the required Python packages:

```bash
pip install -r requirements.txt
```

The main dependency is:
- `docling>=1.0.0` - IBM's document processing library

### Docker Support

The Docker image has been updated to include:
- Python 3 and pip
- Docling package installation
- The Python extraction script

## Usage

The integration is transparent to existing code. When a file with `.xls` extension is processed:

1. `DocumentParserFactory.getParser("xls")` returns a `DoclingParser` instance
2. `DoclingParser` creates a temporary file from the input stream
3. Executes the Python script with the file path and output format
4. Parses the JSON response and returns the extracted text
5. Cleans up temporary files

### Error Handling

The parser handles various error conditions:
- Python not available on the system
- Docling package not installed
- Process execution failures or timeouts
- Invalid JSON responses from the Python script
- File I/O errors

## Configuration

### Timeouts

- Default process timeout: 300 seconds (5 minutes)
- Can be adjusted in `DoclingParser.PROCESS_TIMEOUT_SECONDS`

### Python Command

- Default Python command: `python3`
- Can be modified in `ProcessExecutor.PYTHON_COMMAND`

## Testing

### Unit Tests

- **DocumentParserFactoryTest**: Verifies correct parser selection for different file types
- **ProcessExecutorTest**: Tests process execution functionality
- **DoclingParserTest**: Tests the DoclingParser with mocked process execution

### Integration Testing

For full integration testing with actual Docling:

1. Install Python and Docling on the test system
2. Enable the integration test in `DoclingParserTest.parseToText_RealIntegration_WithMockFile()`
3. Run tests with: `mvn test -Dtest=DoclingParserTest`

## Monitoring and Logging

The integration provides comprehensive logging:

- **INFO**: Successful extractions with character counts
- **WARN**: Missing dependencies, empty extractions
- **ERROR**: Process failures, parsing errors, unexpected exceptions

Log messages include:
- File names being processed
- Process execution results
- Error details and stack traces

## Performance Considerations

- **Process Overhead**: Each extraction spawns a new Python process
- **Temporary Files**: Creates temporary files for each document
- **Memory Usage**: Python process memory is separate from JVM
- **Timeouts**: Long-running extractions are terminated after 5 minutes

## Fallback Strategy

If Docling is not available:
- The parser throws a `TikaException` with a clear error message
- The application can fall back to the `AutoDetectDefaultParser`
- Consider implementing a fallback to `ExcelParser` for XLS files if needed

## Future Enhancements

Potential improvements:
1. **Process Pooling**: Reuse Python processes to reduce startup overhead
2. **Streaming**: Process large files without creating temporary files
3. **Configuration**: Make timeouts and commands configurable via application properties
4. **Caching**: Cache extraction results for identical files
5. **Metrics**: Add metrics for process execution times and success rates

## Troubleshooting

### Common Issues

1. **"Python is not available"**
   - Ensure Python 3 is installed and in PATH
   - Verify with: `python3 --version`

2. **"Docling Python package is not installed"**
   - Install with: `pip install docling`
   - Verify with: `python3 -c "import docling"`

3. **Process timeouts**
   - Check file size and complexity
   - Increase timeout if needed
   - Monitor system resources

4. **Permission errors**
   - Ensure write permissions for temporary directory
   - Check Python script execution permissions

### Debug Mode

Enable debug logging for detailed process execution information:
```properties
logging.level.com.gofore.aita.extraction.utils.ProcessExecutor=DEBUG
logging.level.com.gofore.aita.extraction.parsers.DoclingParser=DEBUG
```

## Security Considerations

- **Process Execution**: Executes external Python processes
- **Temporary Files**: Creates temporary files with document content
- **Input Validation**: Validates file paths and arguments
- **Resource Limits**: Implements timeouts to prevent resource exhaustion

Ensure proper security measures in production environments.
