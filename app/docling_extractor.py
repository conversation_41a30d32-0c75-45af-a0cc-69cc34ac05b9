#!/usr/bin/env python3
"""
Docling-based text extraction script for various document formats.
This script uses the docling library to extract text from documents,
with special optimization for XLS files.
"""

import sys
import json
import logging
import tempfile
import os
from pathlib import Path
from typing import Dict, Any, Optional

try:
    from docling.document_converter import DocumentConverter
    from docling.datamodel.base_models import InputFormat
    from docling.datamodel.pipeline_options import PdfPipelineOptions
    from docling.document_converter import PdfFormatOption
except ImportError as e:
    print(json.dumps({
        "success": False,
        "error": f"Docling library not available: {e}",
        "text": "",
        "metadata": {}
    }))
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)


class DoclingExtractor:
    """Docling-based document text extractor."""
    
    def __init__(self):
        """Initialize the docling converter with optimized settings."""
        # Configure pipeline options for better performance
        pipeline_options = PdfPipelineOptions()
        pipeline_options.do_ocr = False  # Disable OCR for faster processing
        pipeline_options.do_table_structure = True  # Enable table structure for XLS
        
        # Create converter with format-specific options
        self.converter = DocumentConverter(
            format_options={
                InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options)
            }
        )
    
    def extract_text(self, file_path: str, output_format: str = "text") -> Dict[str, Any]:
        """
        Extract text from a document using docling.
        
        Args:
            file_path: Path to the input file
            output_format: Output format ("text" or "xhtml")
            
        Returns:
            Dictionary with extraction results
        """
        try:
            # Validate file exists
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "error": f"File not found: {file_path}",
                    "text": "",
                    "metadata": {}
                }
            
            # Convert document
            result = self.converter.convert(file_path)
            
            if not result:
                return {
                    "success": False,
                    "error": "Docling conversion returned no result",
                    "text": "",
                    "metadata": {}
                }
            
            # Extract text based on format
            if output_format.lower() == "xhtml":
                # For XHTML output, we'll structure the content
                text_content = self._format_as_xhtml(result)
            else:
                # For plain text output
                text_content = result.document.export_to_markdown()
            
            # Extract metadata
            metadata = self._extract_metadata(result)
            
            return {
                "success": True,
                "error": "",
                "text": text_content,
                "metadata": metadata
            }
            
        except Exception as e:
            logger.error(f"Error extracting text from {file_path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "text": "",
                "metadata": {}
            }
    
    def _format_as_xhtml(self, result) -> str:
        """Format the docling result as XHTML."""
        try:
            # Get the document content
            doc = result.document
            
            # Start with basic HTML structure
            xhtml_parts = ["<div class='docling-document'>"]
            
            # Add document title if available
            if hasattr(doc, 'name') and doc.name:
                xhtml_parts.append(f"<h1>{self._escape_html(doc.name)}</h1>")
            
            # Convert to markdown first, then wrap in appropriate HTML tags
            markdown_content = doc.export_to_markdown()
            
            # Simple markdown to HTML conversion for basic formatting
            html_content = self._markdown_to_html(markdown_content)
            xhtml_parts.append(html_content)
            
            xhtml_parts.append("</div>")
            
            return "\n".join(xhtml_parts)
            
        except Exception as e:
            logger.warning(f"Error formatting as XHTML: {e}")
            # Fallback to plain text wrapped in div
            try:
                text_content = result.document.export_to_markdown()
                return f"<div class='docling-document'><pre>{self._escape_html(text_content)}</pre></div>"
            except:
                return "<div class='docling-document'><p>Error formatting content</p></div>"
    
    def _markdown_to_html(self, markdown_text: str) -> str:
        """Simple markdown to HTML conversion."""
        lines = markdown_text.split('\n')
        html_lines = []
        in_table = False
        
        for line in lines:
            line = line.strip()
            if not line:
                if in_table:
                    html_lines.append("</table>")
                    in_table = False
                html_lines.append("<br/>")
                continue
            
            # Handle tables (simple detection)
            if '|' in line and not in_table:
                html_lines.append("<table border='1'>")
                in_table = True
            
            if in_table and '|' in line:
                # Convert table row
                cells = [cell.strip() for cell in line.split('|') if cell.strip()]
                if cells:
                    row_html = "<tr>" + "".join(f"<td>{self._escape_html(cell)}</td>" for cell in cells) + "</tr>"
                    html_lines.append(row_html)
            elif in_table:
                html_lines.append("</table>")
                in_table = False
                html_lines.append(f"<p>{self._escape_html(line)}</p>")
            else:
                # Handle headers
                if line.startswith('# '):
                    html_lines.append(f"<h1>{self._escape_html(line[2:])}</h1>")
                elif line.startswith('## '):
                    html_lines.append(f"<h2>{self._escape_html(line[3:])}</h2>")
                elif line.startswith('### '):
                    html_lines.append(f"<h3>{self._escape_html(line[4:])}</h3>")
                else:
                    html_lines.append(f"<p>{self._escape_html(line)}</p>")
        
        if in_table:
            html_lines.append("</table>")
        
        return "\n".join(html_lines)
    
    def _escape_html(self, text: str) -> str:
        """Escape HTML special characters."""
        if not text:
            return ""
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;'))
    
    def _extract_metadata(self, result) -> Dict[str, Any]:
        """Extract metadata from docling result."""
        metadata = {}
        
        try:
            doc = result.document
            
            # Basic document info
            if hasattr(doc, 'name') and doc.name:
                metadata['title'] = doc.name
            
            # Page count if available
            if hasattr(doc, 'pages') and doc.pages:
                metadata['page_count'] = len(doc.pages)
            
            # Document type
            metadata['converter'] = 'docling'
            
        except Exception as e:
            logger.warning(f"Error extracting metadata: {e}")
        
        return metadata


def main():
    """Main entry point for the script."""
    if len(sys.argv) < 2:
        print(json.dumps({
            "success": False,
            "error": "Usage: python docling_extractor.py <file_path> [output_format]",
            "text": "",
            "metadata": {}
        }))
        sys.exit(1)
    
    file_path = sys.argv[1]
    output_format = sys.argv[2] if len(sys.argv) > 2 else "text"
    
    # Create extractor and process file
    extractor = DoclingExtractor()
    result = extractor.extract_text(file_path, output_format)
    
    # Output result as JSON
    print(json.dumps(result, ensure_ascii=False, indent=None))


if __name__ == "__main__":
    main()
